from collections.abc import AsyncIterator
from uuid import UUID

from langchain_core.messages import HumanMessage
from sqlalchemy.ext.asyncio import AsyncSession

from app.agentic.action import ActionEngine, ActionListItem
from app.agentic.exceptions import ThreadOwnershipError
from app.agentic.graph.graph import Graph<PERSON>ind
from app.agentic.graph.graph_manager import GraphManagerFactory
from app.agentic.repository import OrganizationMemberThreadRepository
from app.agentic.schemas import (
    AccountSummaryRequest,
    ChatRequest,
    ThreadHistoryResponse,
    ThreadRead,
    ThreadsRead,
)
from app.workspace.schemas import OrgEnvironment

# TODO: extract this from the tool definitions
HUMAIN_REVIEW_REQUIRED_TOOLS = [
    "update_opportunity",
    "update_account",
    "create_contact",
    "update_contact",
    "create_task",
    "update_task",
    "create_event",
    "update_event",
]


class AgentService:
    def __init__(
        self,
        org_id: UUID,
        user_id: UUID,
        org_member_id: UUID,
        environment: OrgEnvironment,
        db_session: AsyncSession,
        graph_manager_factory: GraphManagerFactory,
        organization_member_thread_repository: OrganizationMemberThreadRepository,
    ):
        self.org_id = org_id
        self.user_id = user_id
        self.org_member_id = org_member_id
        self.environment = environment
        self.db_session = db_session
        self.graph_manager_factory = graph_manager_factory
        self.organization_member_thread_repository = (
            organization_member_thread_repository
        )

    async def process_message_stream(
        self,
        request: ChatRequest,
    ) -> AsyncIterator[str]:
        if request.role == "user":
            input_messages = [HumanMessage(request.content)]
            resume = None
        else:
            # Happens when a tool call is reviewed by the user
            input_messages = []
            tool_call_results = [
                tc.result
                for tc in request.tool_calls
                if tc.tool_name in HUMAIN_REVIEW_REQUIRED_TOOLS
            ]
            if tool_call_results:
                # TODO: investigate why tool call arg validation fails,
                # for now we just take the last tool call result
                resume = tool_call_results[-1]
            else:
                raise RuntimeError(f"Unhanded tool call results: {request.tool_calls}")

        crm_account_id = request.crm_account_id or ""

        thread_id = request.thread_id
        if thread_id == "":
            raise ValueError("A thread_id is required")
        existing_thread = (
            await self.organization_member_thread_repository.get_by_thread_id(thread_id)
        )
        if existing_thread is None:
            await self.organization_member_thread_repository.create(
                thread_id=thread_id,
                organization_member_id=self.org_member_id,
                environment_id=self.environment.id,
                crm_account_id=crm_account_id,
            )
            await self.db_session.commit()

        graph_manager = await self.graph_manager_factory.create_graph_manager(
            GraphKind.CONVERSATIONAL
        )
        sse_event_iterator = graph_manager.stream_graph(
            messages=input_messages,
            crm_account_id=crm_account_id,
            thread_id=thread_id,
            resume=resume,
            org_id=self.org_id,
            user_id=self.user_id,
        )
        return sse_event_iterator

    async def get_threads_by_org_member_and_crm_account(
        self, account_id: str
    ) -> ThreadsRead | None:
        db_threads = await self.organization_member_thread_repository.get_by_org_member_and_crm_account(
            self.org_member_id, account_id
        )

        if db_threads is None:
            return None

        threads = [
            ThreadRead(
                id=thread.thread_id,
                organization_member_id=thread.organization_member_id,
                crm_account_id=thread.crm_account_id,
                name=thread.thread_name,
                created_at=thread.created_at,
                updated_at=thread.updated_at,
            )
            for thread in db_threads
        ]
        return ThreadsRead(threads=threads)

    async def get_threads_by_org_member_without_crm_account(self) -> ThreadsRead | None:
        db_threads = await self.organization_member_thread_repository.get_by_org_member_without_crm_account(
            self.org_member_id
        )

        if db_threads is None:
            return None

        threads = [
            ThreadRead(
                id=thread.thread_id,
                organization_member_id=thread.organization_member_id,
                crm_account_id=None,
                name=thread.thread_name,
                created_at=thread.created_at,
                updated_at=thread.updated_at,
            )
            for thread in db_threads
        ]
        return ThreadsRead(threads=threads)

    async def get_thread_history(
        self, thread_id: str, page: int, size: int
    ) -> ThreadHistoryResponse:
        thread = await self.organization_member_thread_repository.get_by_thread_id(
            thread_id
        )
        if not thread or thread.organization_member_id != self.org_member_id:
            raise ThreadOwnershipError(
                "Thread not found or does not belong to the current user"
            )

        graph_manager = await self.graph_manager_factory.create_graph_manager(
            GraphKind.CONVERSATIONAL
        )
        return await graph_manager.get_historical_messages(thread_id, page, size)

    async def delete_thread(self, thread_id: str) -> None:
        await self.organization_member_thread_repository.delete_by_thread_id(thread_id)

    async def update_thread_name(self, thread_id: str, thread_name: str) -> None:
        await self.organization_member_thread_repository.set_thread_name(
            thread_id, thread_name
        )

    async def update_thread_crm_account_id(
        self, thread_id: str, crm_account_id: str
    ) -> None:
        thread = await self.organization_member_thread_repository.get_by_thread_id(
            thread_id
        )
        if thread and thread.crm_account_id:
            raise ValueError("CRM account ID is already set")

        await self.organization_member_thread_repository.set_thread_crm_account_id(
            thread_id, crm_account_id
        )

    async def get_actions(self) -> list[ActionListItem]:
        action_engine = ActionEngine(self.db_session)
        return await action_engine.get_available_actions()

    async def create_account_summary(self, request: AccountSummaryRequest) -> None:
        crm_account_id = request.crm_account_id

        if not crm_account_id:
            raise ValueError("CRM account ID is required")

        graph_manager = await self.graph_manager_factory.create_graph_manager(
            GraphKind.ASYNCHRONOUS_SUMMARY
        )
        await graph_manager.invoke_graph(
            {
                "messages": [],
                "crm_account_id": crm_account_id,
                "thread_id": None,
                "org_id": self.org_id,
                "user_id": self.user_id,
            }
        )
