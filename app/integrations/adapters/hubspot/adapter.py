from typing import Any

from app.integrations.adapters.hubspot.handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.base.crm_adapter import BaseCRMAdapter
from app.integrations.schemas import CRMAccountAccessData, CRMMetrics
from app.integrations.types import IntegrationSource


class HubSpotAdapter(BaseCRMAdapter):
    def __init__(self, credentials: ICredentials):
        super().__init__(credentials)
        self._handler = <PERSON>bSpotHandler(credentials=credentials)

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.HUBSPOT

    async def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        return await self._handler.get_opportunity(opportunity_id)

    async def list_opportunities_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.list_opportunities_by_account(
            account_id=account_id, limit=limit, offset=offset
        )

    async def search_opportunities(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.search_opportunities(
            search_criteria=search_criteria, limit=limit, offset=offset
        )

    async def get_account(self, account_id: str) -> dict[str, Any]:
        return await self._handler.get_account(account_id)

    async def search_accounts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.search_accounts(
            search_criteria=search_criteria, limit=limit, offset=offset
        )

    async def get_contact(self, contact_id: str) -> dict[str, Any]:
        return await self._handler.get_contact(contact_id)

    async def list_contacts_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.list_contacts_by_account(
            account_id=account_id, limit=limit, offset=offset
        )

    async def search_contacts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.search_contacts(
            search_criteria=search_criteria, limit=limit, offset=offset
        )

    async def get_task(self, task_id: str) -> dict[str, Any]:
        return await self._handler.get_task(task_id)

    async def list_tasks_by_contact(
        self,
        contact_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.list_tasks_by_contact(
            contact_id=contact_id, limit=limit, offset=offset
        )

    async def list_tasks_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.list_tasks_by_account(
            account_id=account_id, limit=limit, offset=offset
        )

    async def list_tasks_by_opportunity(
        self,
        opportunity_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.list_tasks_by_opportunity(
            opportunity_id=opportunity_id, limit=limit, offset=offset
        )

    async def get_event(self, event_id: str) -> dict[str, Any]:
        return await self._handler.get_event(event_id)

    async def list_events_by_contact(
        self,
        contact_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.list_events_by_contact(
            contact_id=contact_id, limit=limit, offset=offset
        )

    async def list_events_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.list_events_by_account(
            account_id=account_id, limit=limit, offset=offset
        )

    async def list_events_by_opportunity(
        self,
        opportunity_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.list_events_by_opportunity(
            opportunity_id=opportunity_id, limit=limit, offset=offset
        )

    async def resolve_account_access(
        self, crm_user_id: str
    ) -> list[CRMAccountAccessData]:
        return await self._handler.resolve_account_access(crm_user_id)

    async def get_metrics(
        self, crm_user_id: str, field_mapping: dict[str, Any] | None = None
    ) -> CRMMetrics:
        return await self._handler.get_metrics(crm_user_id, field_mapping)
