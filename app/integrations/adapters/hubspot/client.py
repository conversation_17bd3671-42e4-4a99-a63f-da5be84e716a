from typing import Any

from hubspot import Hub<PERSON>pot
from hubspot.crm.companies.exceptions import ApiException as CompaniesApiException
from hubspot.crm.contacts import PublicObjectSearchRequest
from hubspot.crm.contacts.exceptions import ApiException as ContactsApiException
from hubspot.crm.deals.exceptions import ApiException as DealsApiException
from hubspot.crm.objects.exceptions import ApiException as ObjectsApiException

from app.common.helpers.logger import get_logger
from app.common.helpers.to_async import to_async
from app.integrations.types import HubSpotObjectType

logger = get_logger()


class HubSpotClientError(Exception):
    pass


class HubSpotClient:
    def __init__(
        self,
        access_token: str | None = None,
    ):
        self.access_token = access_token
        self._client = None

    def _create_client(self) -> HubSpot:
        try:
            if self.access_token:
                logger.debug("Creating HubSpot client with access token")
                return HubSpot(access_token=self.access_token)
            else:
                raise ValueError("Access token is required for HubSpot authentication")
        except Exception as e:
            logger.exception("Failed to create HubSpot client")
            raise HubSpotClientError(
                f"Failed to create HubSpot client: {str(e)}"
            ) from e

    @property
    def client(self) -> HubSpot:
        if self._client is None:
            self._client = self._create_client()
        return self._client

    @to_async
    def get_object(
        self, object_type: str, object_id: str, properties: list[str] | None = None
    ) -> dict[str, Any]:
        try:
            if object_type.lower() == HubSpotObjectType.CONTACT.value:
                result = self.client.crm.contacts.basic_api.get_by_id(
                    contact_id=object_id, properties=properties
                )
            elif object_type.lower() == HubSpotObjectType.COMPANY.value:
                result = self.client.crm.companies.basic_api.get_by_id(
                    company_id=object_id, properties=properties
                )
            elif object_type.lower() == HubSpotObjectType.DEAL.value:
                result = self.client.crm.deals.basic_api.get_by_id(
                    deal_id=object_id, properties=properties
                )
            else:
                result = self.client.crm.objects.basic_api.get_by_id(
                    object_type=object_type, object_id=object_id, properties=properties
                )

            return result.to_dict()
        except (
            ContactsApiException,
            CompaniesApiException,
            DealsApiException,
            ObjectsApiException,
        ) as e:
            logger.exception(f"Failed to get {object_type} {object_id}")
            raise HubSpotClientError(f"Failed to get {object_type}: {str(e)}") from e

    @to_async
    def search_objects(
        self,
        object_type: str,
        filter_groups: list[dict[str, Any]] | None = None,
        properties: list[str] | None = None,
        limit: int = 100,
        after: str | None = None,
    ) -> dict[str, Any]:
        try:
            search_request = PublicObjectSearchRequest(
                filter_groups=filter_groups or [],
                properties=properties,
                limit=limit,
                after=after,
            )

            if object_type.lower() == HubSpotObjectType.CONTACT.value:
                result = self.client.crm.contacts.search_api.do_search(
                    public_object_search_request=search_request
                )
            elif object_type.lower() == HubSpotObjectType.COMPANY.value:
                result = self.client.crm.companies.search_api.do_search(
                    public_object_search_request=search_request
                )
            elif object_type.lower() == HubSpotObjectType.DEAL.value:
                result = self.client.crm.deals.search_api.do_search(
                    public_object_search_request=search_request
                )
            else:
                result = self.client.crm.objects.search_api.do_search(
                    object_type=object_type, public_object_search_request=search_request
                )

            return result.to_dict()
        except (
            ContactsApiException,
            CompaniesApiException,
            DealsApiException,
            ObjectsApiException,
        ) as e:
            logger.exception(f"Failed to search {object_type}")
            raise HubSpotClientError(f"Failed to search {object_type}: {str(e)}") from e

    @to_async
    def list_objects(
        self,
        object_type: str,
        properties: list[str] | None = None,
        limit: int = 100,
        after: str | None = None,
    ) -> dict[str, Any]:
        try:
            if object_type.lower() == HubSpotObjectType.CONTACT.value:
                result = self.client.crm.contacts.basic_api.get_page(
                    properties=properties, limit=limit, after=after
                )
            elif object_type.lower() == HubSpotObjectType.COMPANY.value:
                result = self.client.crm.companies.basic_api.get_page(
                    properties=properties, limit=limit, after=after
                )
            elif object_type.lower() == HubSpotObjectType.DEAL.value:
                result = self.client.crm.deals.basic_api.get_page(
                    properties=properties, limit=limit, after=after
                )
            else:
                result = self.client.crm.objects.basic_api.get_page(
                    object_type=object_type,
                    properties=properties,
                    limit=limit,
                    after=after,
                )

            return result.to_dict()
        except (
            ContactsApiException,
            CompaniesApiException,
            DealsApiException,
            ObjectsApiException,
        ) as e:
            logger.exception(f"Failed to list {object_type}")
            raise HubSpotClientError(f"Failed to list {object_type}: {str(e)}") from e

    @to_async
    def get_user_info(self) -> dict[str, Any]:
        try:
            response = self.client.api_request(
                {"path": f"/oauth/v1/access-tokens/{self.access_token}"}
            )
            return response.json()
        except Exception as e:
            logger.exception("Failed to get user info")
            raise HubSpotClientError(f"Failed to get user info: {str(e)}") from e
