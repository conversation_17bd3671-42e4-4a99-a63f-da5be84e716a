from datetime import datetime
from io import Bytes<PERSON>
from unittest.mock import AsyncMock, <PERSON><PERSON>

import pytest

from app.integrations.adapters.gcs.adapter import GCSAdapter
from app.integrations.adapters.gcs.client import GCSClient
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.schemas import FileData
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_credentials():
    """Mock credentials with GCS key."""
    credentials = Mock(spec=ICredentials)
    credentials.secrets = {
        "gcs_key": {
            "type": "service_account",
            "project_id": "test-project",
            "private_key_id": "test-key-id",
            "private_key": "abcd",
        }
    }
    return credentials


@pytest.fixture
def mock_credentials_no_gcs_key():
    credentials = Mock(spec=ICredentials)
    credentials.secrets = {"other_key": "other_value"}
    return credentials


@pytest.fixture
def mock_credentials_empty_secrets():
    credentials = Mock(spec=ICredentials)
    credentials.secrets = {}
    return credentials


@pytest.fixture
def mock_credentials_none_secrets():
    credentials = Mock(spec=ICredentials)
    credentials.secrets = None
    return credentials


@pytest.fixture
def mock_adapter(mocker, mock_credentials):
    mock_client = AsyncMock(spec=GCSClient)
    mocker.patch(
        "app.integrations.adapters.gcs.adapter.GCSClient", return_value=mock_client
    )

    adapter = GCSAdapter(mock_credentials)
    return adapter


@pytest.fixture
def mock_file_objects():
    """Mock file objects returned by GCS client."""
    mock_file1 = Mock()
    mock_file1.id = "file1-id"
    mock_file1.name = "document.pdf"
    mock_file1.size = 1024
    mock_file1.time_created = datetime(2025, 1, 1, 12, 0, 0)
    mock_file1.updated = datetime(2025, 1, 2, 12, 0, 0)
    mock_file1.md5_hash = "hash1"
    mock_file1.content_type = "application/pdf"

    mock_file2 = Mock()
    mock_file2.id = "file2-id"
    mock_file2.name = "image.jpg"
    mock_file2.size = 2048
    mock_file2.time_created = datetime(2025, 1, 3, 12, 0, 0)
    mock_file2.updated = datetime(2025, 1, 4, 12, 0, 0)
    mock_file2.md5_hash = "hash2"
    mock_file2.content_type = "image/jpeg"

    mock_file3 = Mock()
    mock_file3.id = "file3-id"
    mock_file3.name = "data.txt"
    mock_file3.size = 512
    mock_file3.time_created = datetime(2025, 1, 5, 12, 0, 0)
    mock_file3.updated = datetime(2025, 1, 6, 12, 0, 0)
    mock_file3.md5_hash = "hash3"
    mock_file3.content_type = "text/plain"

    return [mock_file1, mock_file2, mock_file3]


def test_init_with_valid_credentials(mocker, mock_credentials):
    mock_client_instance = Mock(spec=GCSClient)
    mock_gcs_client_class = mocker.patch(
        "app.integrations.adapters.gcs.adapter.GCSClient",
        return_value=mock_client_instance,
    )

    adapter = GCSAdapter(mock_credentials)

    mock_gcs_client_class.assert_called_once_with(
        gcs_credentials=mock_credentials.secrets["gcs_key"]
    )
    assert adapter._client == mock_client_instance


def test_init_with_missing_gcs_key(mock_credentials_no_gcs_key):
    with pytest.raises(ValueError, match="GCS key not found in credentials"):
        GCSAdapter(mock_credentials_no_gcs_key)


def test_init_with_empty_secrets(mock_credentials_empty_secrets):
    with pytest.raises(ValueError, match="GCS key not found in credentials"):
        GCSAdapter(mock_credentials_empty_secrets)


def test_init_with_none_secrets(mock_credentials_none_secrets):
    with pytest.raises((ValueError, TypeError)):
        GCSAdapter(mock_credentials_none_secrets)


def test_source_property(mocker, mock_credentials):
    mocker.patch("app.integrations.adapters.gcs.adapter.GCSClient")
    adapter = GCSAdapter(mock_credentials)
    assert adapter.source == IntegrationSource.GCS


# File operation tests
@pytest.mark.anyio
async def test_upload_file(mock_adapter):
    """Test file upload."""
    file_content = b"test file content"
    file_obj = BytesIO(file_content)
    bucket_name = "test-bucket"
    file_name = "test-file.txt"

    await mock_adapter.upload_file(bucket_name, file_obj, file_name)

    mock_adapter._client.upload_file.assert_called_once_with(
        bucket_name=bucket_name, file_obj=file_obj, file_name=file_name
    )


@pytest.mark.anyio
async def test_upload_file_exception(mock_adapter):
    file_obj = BytesIO(b"test content")
    mock_adapter._client.upload_file.side_effect = Exception("Upload failed")

    with pytest.raises(Exception, match="Upload failed"):
        await mock_adapter.upload_file("test-bucket", file_obj, "test-file.txt")


@pytest.mark.anyio
async def test_download_file(mock_adapter):
    bucket_name = "test-bucket"
    file_name = "test-file.txt"
    expected_content = b"downloaded file content"

    mock_adapter._client.download_file.return_value = expected_content

    result = await mock_adapter.download_file(bucket_name, file_name)

    mock_adapter._client.download_file.assert_called_once_with(bucket_name, file_name)
    assert result == expected_content


@pytest.mark.anyio
async def test_download_file_exception(mock_adapter):
    mock_adapter._client.download_file.side_effect = Exception("Download failed")

    with pytest.raises(Exception, match="Download failed"):
        await mock_adapter.download_file("test-bucket", "test-file.txt")


@pytest.mark.anyio
async def test_delete_file(mock_adapter):
    bucket_name = "test-bucket"
    file_name = "test-file.txt"

    await mock_adapter.delete_file(bucket_name, file_name)

    mock_adapter._client.delete_file.assert_called_once_with(bucket_name, file_name)


@pytest.mark.anyio
async def test_delete_file_exception(mock_adapter):
    mock_adapter._client.delete_file.side_effect = Exception("Delete failed")

    with pytest.raises(Exception, match="Delete failed"):
        await mock_adapter.delete_file("test-bucket", "test-file.txt")


@pytest.mark.anyio
async def test_list_files(mock_adapter, mock_file_objects):
    bucket_name = "test-bucket"

    mock_adapter._client.list_files.return_value = mock_file_objects

    result = await mock_adapter.list_files(bucket_name)

    mock_adapter._client.list_files.assert_called_once_with(bucket_name)

    assert len(result) == 3

    assert isinstance(result[0], FileData)
    assert result[0].id == "file1-id"
    assert result[0].name == "document.pdf"
    assert result[0].md5_hash == "hash1"

    assert isinstance(result[1], FileData)
    assert result[1].id == "file2-id"
    assert result[1].name == "image.jpg"
    assert result[1].md5_hash == "hash2"

    assert isinstance(result[2], FileData)
    assert result[2].id == "file3-id"
    assert result[2].name == "data.txt"
    assert result[2].md5_hash == "hash3"


@pytest.mark.anyio
async def test_list_files_empty_bucket(mock_adapter):
    """Test listing files from empty bucket."""
    bucket_name = "test-bucket"

    mock_adapter._client.list_files.return_value = []

    result = await mock_adapter.list_files(bucket_name)

    mock_adapter._client.list_files.assert_called_once_with(bucket_name)
    assert len(result) == 0


@pytest.mark.anyio
async def test_list_files_exception(mock_adapter):
    """Test list_files when client raises an exception."""
    bucket_name = "test-bucket"

    mock_adapter._client.list_files.side_effect = Exception("List files failed")

    with pytest.raises(Exception, match="List files failed"):
        await mock_adapter.list_files(bucket_name)


def test_client_creation_failure(mocker, mock_credentials):
    mocker.patch(
        "app.integrations.adapters.gcs.adapter.GCSClient",
        side_effect=Exception("Client creation failed"),
    )

    with pytest.raises(Exception, match="Client creation failed"):
        GCSAdapter(mock_credentials)


@pytest.mark.anyio
async def test_create_bucket(mock_adapter):
    bucket_name = "test-bucket"

    await mock_adapter.create_bucket(bucket_name)

    mock_adapter._client.create_bucket.assert_called_once_with(bucket_name)


@pytest.mark.anyio
async def test_create_bucket_exception(mock_adapter):
    bucket_name = "test-bucket"
    mock_adapter._client.create_bucket.side_effect = Exception("Bucket creation failed")

    with pytest.raises(Exception, match="Bucket creation failed"):
        await mock_adapter.create_bucket(bucket_name)


@pytest.mark.anyio
async def test_bucket_exists_true(mock_adapter):
    bucket_name = "existing-bucket"
    mock_adapter._client.bucket_exists.return_value = True

    result = await mock_adapter.bucket_exists(bucket_name)

    mock_adapter._client.bucket_exists.assert_called_once_with(bucket_name)
    assert result is True


@pytest.mark.anyio
async def test_bucket_exists_false(mock_adapter):
    bucket_name = "nonexistent-bucket"
    mock_adapter._client.bucket_exists.return_value = False

    result = await mock_adapter.bucket_exists(bucket_name)

    mock_adapter._client.bucket_exists.assert_called_once_with(bucket_name)
    assert result is False


@pytest.mark.anyio
async def test_bucket_exists_exception(mock_adapter):
    bucket_name = "test-bucket"
    mock_adapter._client.bucket_exists.side_effect = Exception("Bucket check failed")

    with pytest.raises(Exception, match="Bucket check failed"):
        await mock_adapter.bucket_exists(bucket_name)
