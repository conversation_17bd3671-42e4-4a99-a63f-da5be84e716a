import asyncio
import uuid

import typer

from app.integrations.types import IntegrationSource
from app.workspace.integrations.workflows.file import FileWorkflow
from app.workspace.types import EnvironmentType

app = typer.Typer(help="File processing management commands", no_args_is_help=True)


@app.command()
def start_processing(
    source: IntegrationSource = typer.Argument(..., help="Integration source"),
    org_id: uuid.UUID = typer.Option(..., help="Organization ID"),
    bucket_names: list[str] = typer.Option(
        ..., "--bucket", "-b", help="Bucket names to process"
    ),
    env: EnvironmentType = typer.Option(EnvironmentType.PROD, help="Environment type"),
):
    if not bucket_names:
        typer.echo("At least one bucket name is required")
        raise typer.Exit(code=1)

    try:
        result = asyncio.run(
            _start_processing(
                source=source,
                org_id=org_id,
                bucket_names=bucket_names,
                env=env,
            )
        )

        if result["status"] == "success":
            typer.echo(f"{source.value} files processing completed successfully")
            if result.get("details"):
                typer.echo(str(result["details"]))

        else:
            typer.echo(f"Error: {result.get('error', 'Unknown error')}")
            raise typer.Exit(code=1)

    except Exception as e:
        typer.echo(f"Error starting {source.value} processing: {e}")
        raise typer.Exit(code=1)


async def _start_processing(
    source: IntegrationSource,
    org_id: uuid.UUID,
    bucket_names: list[str],
    env: EnvironmentType,
) -> dict:
    async with FileWorkflow(org_id=org_id, source=source, env_type=env) as workflow:
        result = await workflow.start_processing(
            bucket_names=bucket_names,
        )
        return result
