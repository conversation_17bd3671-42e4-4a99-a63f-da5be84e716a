from abc import ABC, abstractmethod

from app.integrations.schemas import CRMAccountAccessSlice


class ICRMStore(ABC):
    """
    Interface for storing CRM data.

    This abstraction decouples synchronization logic from the specific
    storage implementation (database, cache, etc.). It potentially handles
    multiple types of CRM data such as account access, opportunities,
    contacts, etc.
    """

    @abstractmethod
    async def clear_user_account_access(self, user_id: str) -> None:
        """
        Remove all existing account access records for a given user.

        This is typically called before storing new access records to ensure
        a complete replacement of the user's access data.
        """
        pass

    @abstractmethod
    async def store_account_access(
        self, access_slice: CRMAccountAccessSlice
    ) -> tuple[int, int]:
        """
        Store account access information for a user, replacing any existing access.

        This method completely replaces the user's previous access records with
        the new data provided. It first clears all existing access records for
        the user, then stores the new access records.

        Returns: tuple (Number of access records stored, Number of access records deleted)
        """
        pass

    @abstractmethod
    async def get_user_account_access(self, user_id: str) -> CRMAccountAccessSlice:
        """
        Retrieve account access information for a user.
        Returns: Data slice containing the user's account access records
        """
        pass

    @abstractmethod
    async def store_account_summary(
        self, user_id: str, crm_account_id: str, summary: str
    ) -> None:
        """
        Store or update the summary for a specific account access record.
        """
        pass
