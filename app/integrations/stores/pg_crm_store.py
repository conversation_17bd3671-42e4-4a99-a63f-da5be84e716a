import uuid

from sqlalchemy import Delete, Select, Update, delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.integrations.base.crm_store import ICRMStore
from app.integrations.models import CRMAccountAccess
from app.integrations.schemas import CRMA<PERSON>untAccessD<PERSON>, CRMAccountAccessSlice
from app.integrations.types import IntegrationSource


class PostgresCRMStore(ICRMStore):
    def __init__(
        self,
        tenant_id: uuid.UUID,
        source: IntegrationSource,
        session: AsyncSession,
    ):
        self.tenant_id = tenant_id
        self.source = source
        self.session = session

    async def clear_user_account_access(self, user_id: str) -> None:
        await self._delete_user_account_access(user_id)
        await self.session.commit()

    async def store_account_access(
        self, access_slice: CRMAccountAccessSlice
    ) -> tuple[int, int]:
        user_id = access_slice.user_id

        deleted_count = await self._delete_user_account_access(user_id)

        for account in access_slice.accounts:
            access = CRMAccountAccess(
                id=uuid.uuid4(),
                tenant_id=self.tenant_id,
                source=self.source,
                crm_account_id=account.account_id,
                crm_user_id=user_id,
                crm_account_name=account.account_name,
                crm_access_role=account.access_role,
                crm_access_type=account.access_type,
            )
            self.session.add(access)

        await self.session.commit()

        return len(access_slice.accounts), deleted_count

    async def get_user_account_access(self, user_id: str) -> CRMAccountAccessSlice:
        stmt = self._build_scoped_stmt(
            CRMAccountAccess,
            select(CRMAccountAccess),
            CRMAccountAccess.crm_user_id == user_id,
        )

        result = await self.session.execute(stmt)
        access_records = [
            CRMAccountAccessData(
                account_id=access.crm_account_id,
                account_name=access.crm_account_name,
                access_type=access.crm_access_type,
                access_role=access.crm_access_role,
            )
            for access in result.scalars().all()
        ]

        return CRMAccountAccessSlice(
            user_id=user_id,
            accounts=access_records,
        )

    async def _delete_user_account_access(self, user_id: str) -> int:
        delete_stmt = self._build_scoped_stmt(
            CRMAccountAccess,
            delete(CRMAccountAccess),
            CRMAccountAccess.crm_user_id == user_id,
        )
        result = await self.session.execute(delete_stmt)
        return result.rowcount

    async def store_account_summary(
        self, user_id: str, crm_account_id: str, summary: str
    ) -> None:
        update_stmt = self._build_scoped_stmt(
            CRMAccountAccess,
            update(CRMAccountAccess),
            CRMAccountAccess.crm_user_id == user_id,
            CRMAccountAccess.crm_account_id == crm_account_id,
        ).values(summary=summary)

        await self.session.execute(update_stmt)
        await self.session.commit()

    def _build_scoped_stmt(
        self,
        model: type[CRMAccountAccess],
        stmt: Select | Delete | Update,
        *conditions,
    ):
        return stmt.where(
            model.tenant_id == self.tenant_id,
            model.source == self.source,
            *conditions,
        )
