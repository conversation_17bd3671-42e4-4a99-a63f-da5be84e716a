from typing import Any, Literal

from app.common.helpers.logger import get_logger
from app.integrations.adapters.hubspot.access_resolver import (
    HubSpotAccountAccessResolver,
)
from app.integrations.adapters.hubspot.refreshable_client_mixin import (
    HubSpotRefreshableClientMixin,
)
from app.integrations.base.credentials_resolver import (
    ICredentials,
)
from app.integrations.schemas import CRMAccountAccessData, CRMMetrics
from app.integrations.types import HubSpotObjectType

logger = get_logger()


class HubSpotHandler(HubSpotRefreshableClientMixin):
    def __init__(self, credentials: ICredentials):
        self.init_hubspot_client(credentials)

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        return await self.hubspot_client.get_object(
            HubSpotObjectType.DEAL.value, opportunity_id
        )

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def list_opportunities_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.company",
                        "operator": "EQ",
                        "value": account_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.DEAL.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def search_opportunities(
        self,
        search_criteria: dict[str, Any],
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        filters = []

        for field, value in search_criteria.items():
            if value:
                if field.lower() in ["dealname", "dealstage"]:
                    filters.append(
                        {
                            "propertyName": field,
                            "operator": "CONTAINS_TOKEN",
                            "value": str(value),
                        }
                    )
                elif field.lower() in ["amount", "closedate"]:
                    filters.append(
                        {"propertyName": field, "operator": "EQ", "value": str(value)}
                    )
                else:
                    filters.append(
                        {"propertyName": field, "operator": "EQ", "value": str(value)}
                    )

        if not filters:
            return []

        filter_groups = [{"filters": filters}]
        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.DEAL.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def get_account(self, account_id: str) -> dict[str, Any]:
        return await self.hubspot_client.get_object(
            HubSpotObjectType.COMPANY.value, account_id
        )

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def search_accounts(
        self,
        search_criteria: dict[str, Any],
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        filters = []

        for field, value in search_criteria.items():
            if value:
                if field.lower() in ["name", "domain"]:
                    filters.append(
                        {
                            "propertyName": field,
                            "operator": "CONTAINS_TOKEN",
                            "value": str(value),
                        }
                    )
                else:
                    filters.append(
                        {"propertyName": field, "operator": "EQ", "value": str(value)}
                    )

        if not filters:
            return []

        filter_groups = [{"filters": filters}]
        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.COMPANY.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def get_contact(self, contact_id: str) -> dict[str, Any]:
        return await self.hubspot_client.get_object(
            HubSpotObjectType.CONTACT.value, contact_id
        )

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def list_contacts_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.company",
                        "operator": "EQ",
                        "value": account_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.CONTACT.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def search_contacts(
        self,
        search_criteria: dict[str, Any],
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        filters = []

        for field, value in search_criteria.items():
            if value:
                if field.lower() in ["firstname", "lastname", "email"]:
                    filters.append(
                        {
                            "propertyName": field,
                            "operator": "CONTAINS_TOKEN",
                            "value": str(value),
                        }
                    )
                else:
                    filters.append(
                        {"propertyName": field, "operator": "EQ", "value": str(value)}
                    )

        if not filters:
            return []

        filter_groups = [{"filters": filters}]
        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.CONTACT.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def get_task(self, task_id: str) -> dict[str, Any]:
        return await self.hubspot_client.get_object(
            HubSpotObjectType.TASK.value, task_id
        )

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def list_tasks_by_contact(
        self,
        contact_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not contact_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.contact",
                        "operator": "EQ",
                        "value": contact_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.TASK.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def list_tasks_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.company",
                        "operator": "EQ",
                        "value": account_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.TASK.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def list_tasks_by_opportunity(
        self,
        opportunity_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not opportunity_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.deal",
                        "operator": "EQ",
                        "value": opportunity_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.TASK.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def get_event(self, event_id: str) -> dict[str, Any]:
        return await self.hubspot_client.get_object(
            HubSpotObjectType.MEETING.value, event_id
        )

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def list_events_by_contact(
        self,
        contact_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not contact_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.contact",
                        "operator": "EQ",
                        "value": contact_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.MEETING.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def list_events_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.company",
                        "operator": "EQ",
                        "value": account_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.MEETING.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def list_events_by_opportunity(
        self,
        opportunity_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not opportunity_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.deal",
                        "operator": "EQ",
                        "value": opportunity_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.MEETING.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def resolve_account_access(
        self, crm_user_id: str
    ) -> list[CRMAccountAccessData]:
        resolver = HubSpotAccountAccessResolver(client=self.hubspot_client)
        return await resolver.get_user_account_access(crm_user_id)

    @HubSpotRefreshableClientMixin.handle_expired_session
    async def get_metrics(self, crm_user_id: str) -> CRMMetrics:
        logger.info(f"Getting metrics for HubSpot user: {crm_user_id}")
        # TODO: Implement HubSpot metrics
        return CRMMetrics(
            quota=0,
            closed_won=0,
            pipeline=0,
            forecast=0,
        )
