import pytest

from app.integrations.adapters.salesforce.adapter import SalesforceAdapter
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_credentials(mocker):
    mock_creds = mocker.Mock(spec=ICredentials)
    mock_creds.secrets = {
        "username": "test_username",
        "password": "test_password",
        "security_token": "test_token",
    }
    return mock_creds


@pytest.fixture(autouse=True)
def mock_salesforce_handler(mocker):
    mock_handler = mocker.MagicMock()

    async def mock_get_opportunity(*_args, **_kwargs):
        return {"Id": "001", "Name": "Test Opportunity"}

    async def mock_list_opportunities_by_account(*_args, **_kwargs):
        return [{"Id": "001", "Name": "Test Opportunity"}]

    async def mock_search_opportunities(*_args, **_kwargs):
        return [{"Id": "001", "Name": "Found Opportunity"}]

    async def mock_get_account(*_args, **_kwargs):
        return {"Id": "002", "Name": "Test Account"}

    async def mock_search_accounts(*_args, **_kwargs):
        return [{"Id": "002", "Name": "Found Account"}]

    async def mock_get_contact(*_args, **_kwargs):
        return {"Id": "003", "Name": "Test Contact"}

    async def mock_list_contacts_by_account(*_args, **_kwargs):
        return [{"Id": "003", "Name": "Test Contact"}]

    async def mock_search_contacts(*_args, **_kwargs):
        return [{"Id": "003", "Name": "Found Contact"}]

    async def mock_get_task(*_args, **_kwargs):
        return {"Id": "00T", "Subject": "Test Task"}

    async def mock_list_tasks_by_contact(*_args, **_kwargs):
        return [{"Id": "00T", "Subject": "Contact Task"}]

    async def mock_list_tasks_by_account(*_args, **_kwargs):
        return [{"Id": "00T", "Subject": "Account Task"}]

    async def mock_get_event(*_args, **_kwargs):
        return {"Id": "00U", "Subject": "Test Event"}

    async def mock_list_events_by_contact(*_args, **_kwargs):
        return [{"Id": "00U", "Subject": "Contact Event"}]

    async def mock_list_events_by_account(*_args, **_kwargs):
        return [{"Id": "00U", "Subject": "Account Event"}]

    async def mock_resolve_account_access(*_args, **_kwargs):
        from app.integrations.schemas import CRMAccountAccessData

        return [
            CRMAccountAccessData(
                account_id="002",
                account_name="Test Account",
                access_type="owner",
                access_role=None,
            )
        ]

    mock_handler.get_opportunity = mock_get_opportunity
    mock_handler.list_opportunities_by_account = mock_list_opportunities_by_account
    mock_handler.search_opportunities = mock_search_opportunities
    mock_handler.get_account = mock_get_account
    mock_handler.search_accounts = mock_search_accounts
    mock_handler.get_contact = mock_get_contact
    mock_handler.list_contacts_by_account = mock_list_contacts_by_account
    mock_handler.search_contacts = mock_search_contacts
    mock_handler.get_task = mock_get_task
    mock_handler.list_tasks_by_contact = mock_list_tasks_by_contact
    mock_handler.list_tasks_by_account = mock_list_tasks_by_account
    mock_handler.get_event = mock_get_event
    mock_handler.list_events_by_contact = mock_list_events_by_contact
    mock_handler.list_events_by_account = mock_list_events_by_account
    mock_handler.resolve_account_access = mock_resolve_account_access

    handler_class = mocker.patch(
        "app.integrations.adapters.salesforce.adapter.SalesforceHandler"
    )
    handler_class.return_value = mock_handler
    return mock_handler


@pytest.fixture
def salesforce_adapter(mock_credentials):
    return SalesforceAdapter(credentials=mock_credentials)


def test_init(mocker, mock_credentials):
    handler_class = mocker.patch(
        "app.integrations.adapters.salesforce.adapter.SalesforceHandler"
    )
    adapter = SalesforceAdapter(credentials=mock_credentials)
    assert adapter.credentials == mock_credentials
    handler_class.assert_called_once_with(credentials=mock_credentials)


def test_source(salesforce_adapter):
    assert salesforce_adapter.source == IntegrationSource.SALESFORCE


@pytest.mark.anyio
async def test_get_opportunity(salesforce_adapter):
    opportunity_id = "opp123"
    result = await salesforce_adapter.get_opportunity(opportunity_id)
    assert result == {"Id": "001", "Name": "Test Opportunity"}


@pytest.mark.anyio
async def test_list_opportunities_by_account(salesforce_adapter):
    account_id = "account456"
    limit = 50
    offset = 10
    result = await salesforce_adapter.list_opportunities_by_account(
        account_id=account_id,
        limit=limit,
        offset=offset,
    )
    assert len(result) == 1
    assert result[0]["Id"] == "001"
    assert result[0]["Name"] == "Test Opportunity"


@pytest.mark.anyio
async def test_search_opportunities(salesforce_adapter):
    search_criteria = {"Name": "Big Deal"}
    limit = 50
    offset = 10
    result = await salesforce_adapter.search_opportunities(
        search_criteria=search_criteria, limit=limit, offset=offset
    )
    assert len(result) == 1
    assert result[0]["Id"] == "001"
    assert result[0]["Name"] == "Found Opportunity"


@pytest.mark.anyio
async def test_get_account(salesforce_adapter):
    account_id = "acc123"
    result = await salesforce_adapter.get_account(account_id)
    assert result == {"Id": "002", "Name": "Test Account"}


@pytest.mark.anyio
async def test_search_accounts(salesforce_adapter):
    search_criteria = {"Name": "Tech Corp"}
    limit = 50
    offset = 10
    result = await salesforce_adapter.search_accounts(
        search_criteria=search_criteria, limit=limit, offset=offset
    )
    assert len(result) == 1
    assert result[0]["Id"] == "002"
    assert result[0]["Name"] == "Found Account"


@pytest.mark.anyio
async def test_resolve_account_access(salesforce_adapter):
    user_id = "user123"
    result = await salesforce_adapter.resolve_account_access(user_id)
    assert len(result) == 1
    assert result[0].account_id == "002"
    assert result[0].account_name == "Test Account"
    assert result[0].access_type == "owner"
    assert result[0].access_role is None


@pytest.mark.anyio
async def test_get_contact(salesforce_adapter):
    contact_id = "con123"
    result = await salesforce_adapter.get_contact(contact_id)
    assert result == {"Id": "003", "Name": "Test Contact"}


@pytest.mark.anyio
async def test_list_contacts_by_account(salesforce_adapter):
    account_id = "acc123"
    limit = 50
    offset = 10
    result = await salesforce_adapter.list_contacts_by_account(
        account_id=account_id, limit=limit, offset=offset
    )
    assert len(result) == 1
    assert result[0]["Id"] == "003"
    assert result[0]["Name"] == "Test Contact"


@pytest.mark.anyio
async def test_search_contacts(salesforce_adapter):
    search_criteria = {"Email": "<EMAIL>"}
    limit = 50
    offset = 10
    result = await salesforce_adapter.search_contacts(
        search_criteria=search_criteria, limit=limit, offset=offset
    )
    assert len(result) == 1
    assert result[0]["Id"] == "003"
    assert result[0]["Name"] == "Found Contact"


@pytest.mark.anyio
async def test_get_task(salesforce_adapter):
    task_id = "tsk123"
    result = await salesforce_adapter.get_task(task_id)
    assert result == {"Id": "00T", "Subject": "Test Task"}


@pytest.mark.anyio
async def test_list_tasks_by_contact(salesforce_adapter):
    contact_id = "con123"
    limit = 50
    offset = 10
    result = await salesforce_adapter.list_tasks_by_contact(
        contact_id=contact_id, limit=limit, offset=offset
    )
    assert len(result) == 1
    assert result[0]["Id"] == "00T"
    assert result[0]["Subject"] == "Contact Task"


@pytest.mark.anyio
async def test_list_tasks_by_account(salesforce_adapter):
    account_id = "acc123"
    limit = 50
    offset = 10
    result = await salesforce_adapter.list_tasks_by_account(
        account_id=account_id, limit=limit, offset=offset
    )
    assert len(result) == 1
    assert result[0]["Id"] == "00T"
    assert result[0]["Subject"] == "Account Task"


@pytest.mark.anyio
async def test_get_event(salesforce_adapter):
    event_id = "evt123"
    result = await salesforce_adapter.get_event(event_id)
    assert result == {"Id": "00U", "Subject": "Test Event"}


@pytest.mark.anyio
async def test_list_events_by_contact(salesforce_adapter):
    contact_id = "con123"
    limit = 50
    offset = 10
    result = await salesforce_adapter.list_events_by_contact(
        contact_id=contact_id, limit=limit, offset=offset
    )
    assert len(result) == 1
    assert result[0]["Id"] == "00U"
    assert result[0]["Subject"] == "Contact Event"


@pytest.mark.anyio
async def test_list_events_by_account(salesforce_adapter):
    account_id = "acc123"
    limit = 50
    offset = 10
    result = await salesforce_adapter.list_events_by_account(
        account_id=account_id, limit=limit, offset=offset
    )
    assert len(result) == 1
    assert result[0]["Id"] == "00U"
    assert result[0]["Subject"] == "Account Event"
