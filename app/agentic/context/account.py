import async<PERSON>
from typing import Any
from uuid import UUID

from app.common.helpers.logger import get_logger
from app.workspace.integrations.user_integrations import UserIntegrations

logger = get_logger()


async def get_account_details(
    user_id: UUID,
    crm_account_id: str,
    user_integrations: UserIntegrations,
):
    crm_provider = await user_integrations.crm()

    if not crm_provider:
        raise RuntimeError(f"No CRM integration configured for user {user_id}")

    account_details = await crm_provider.get_account(crm_account_id)

    return account_details


async def get_comprehensive_crm_data(
    user_id: UUID,
    crm_account_id: str,
    user_integrations: UserIntegrations,
    opportunities_limit: int = 50,
    contacts_limit: int = 50,
) -> tuple[dict[str, Any], list[dict[str, Any]], list[dict[str, Any]]]:
    try:
        crm_provider = await user_integrations.crm()

        if not crm_provider:
            logger.warning(f"No CRM integration configured for user {user_id}")
            return {}, [], []
    except Exception as e:
        logger.exception(f"Failed to get CRM provider for user {user_id}: {e}")
        return {}, [], []

    async def fetch_account() -> dict[str, Any]:
        try:
            return await crm_provider.get_account(crm_account_id)
        except Exception as e:
            logger.exception(f"Failed to fetch account {crm_account_id}: {e}")
            return {}

    async def fetch_opportunities() -> list[dict[str, Any]]:
        try:
            opportunities = await crm_provider.list_opportunities_by_account(
                crm_account_id, limit=opportunities_limit
            )

            opportunities_details = []
            for opp in opportunities:
                try:
                    opp_details = await crm_provider.get_opportunity(opp["Id"])
                    opportunities_details.append(opp_details)
                except Exception as e:
                    logger.exception(
                        f"Failed to fetch opportunity {opp['Id']} for account {crm_account_id}: {e}"
                    )

            return opportunities_details
        except Exception as e:
            logger.exception(
                f"Failed to fetch opportunities for account {crm_account_id}: {e}"
            )
            return []

    async def fetch_contacts() -> list[dict[str, Any]]:
        try:
            contacts = await crm_provider.list_contacts_by_account(
                crm_account_id, limit=contacts_limit
            )

            contacts_details = []
            for contact in contacts:
                try:
                    contact_details = await crm_provider.get_contact(contact["Id"])
                    contacts_details.append(contact_details)
                except Exception as e:
                    logger.exception(
                        f"Failed to fetch contact {contact['Id']} for account {crm_account_id}: {e}"
                    )

            return contacts_details
        except Exception as e:
            logger.exception(
                f"Failed to fetch contacts for account {crm_account_id}: {e}"
            )
            return []

    account_info, opportunities_info, contacts_info = await asyncio.gather(
        fetch_account(),
        fetch_opportunities(),
        fetch_contacts(),
        return_exceptions=False,
    )

    return account_info, opportunities_info, contacts_info
