from typing import Binary<PERSON>

from google.cloud.storage import Blob, Client
from google.oauth2.service_account import Credentials

from app.common.helpers.logger import get_logger
from app.common.helpers.to_async import to_async

logger = get_logger()


class GCSClientError(Exception):
    pass


class GCSClient:
    def __init__(
        self,
        gcs_credentials: dict,
    ):
        self.gcs_credentials = Credentials.from_service_account_info(gcs_credentials)

    def _create_client(self) -> Client:
        try:
            return Client(credentials=self.gcs_credentials)
        except Exception as e:
            logger.exception("Failed to create GCP client")
            raise GCSClientError(f"Client initialization failed: {str(e)}")

    @property
    def _client(self) -> Client:
        if not hasattr(self, "_lazy_client"):
            self._lazy_client = self._create_client()
        return self._lazy_client

    @to_async
    def upload_file(self, bucket_name: str, file_obj: BinaryIO, file_name: str) -> None:
        try:
            blob = self._client.bucket(bucket_name).blob(file_name)
            blob.upload_from_file(file_obj, content_type="application/octet-stream")
        except Exception as e:
            logger.exception(f"Failed to upload {file_name} to bucket {bucket_name}")
            raise GCSClientError(f"Failed to upload {file_name}: {str(e)}")

    @to_async
    def download_file(self, bucket_name: str, file_name: str) -> bytes:
        try:
            blob = self._client.bucket(bucket_name).blob(file_name)
            return blob.download_as_bytes()
        except Exception as e:
            logger.exception(
                f"Failed to download {file_name} from bucket {bucket_name}"
            )
            raise GCSClientError(f"Failed to download {file_name}: {str(e)}")

    @to_async
    def delete_file(self, bucket_name: str, file_name: str) -> None:
        try:
            blob = self._client.bucket(bucket_name).blob(file_name)
            blob.delete()
        except Exception as e:
            logger.exception(f"Failed to delete {file_name} from bucket {bucket_name}")
            raise GCSClientError(f"Failed to delete {file_name}: {str(e)}")

    @to_async
    def list_files(self, bucket_name: str) -> list[Blob]:
        try:
            bucket = self._client.bucket(bucket_name)
            return list(bucket.list_blobs())
        except Exception as e:
            logger.exception(f"Failed to list files from bucket {bucket_name}")
            raise GCSClientError(f"Failed to list files: {str(e)}")

    @to_async
    def create_bucket(self, bucket_name: str, location: str = "EU") -> None:
        try:
            bucket = self._client.bucket(bucket_name)
            bucket = self._client.create_bucket(bucket, location=location)
            logger.info(f"Bucket {bucket_name} created successfully")
        except Exception as e:
            if any(
                keyword in str(e).lower() for keyword in ["already exists", "conflict"]
            ):
                logger.info(f"Bucket {bucket_name} already exists")
                return
            logger.exception(f"Failed to create bucket {bucket_name}")
            raise GCSClientError(f"Failed to create bucket: {str(e)}")

    @to_async
    def bucket_exists(self, bucket_name: str) -> bool:
        try:
            bucket = self._client.bucket(bucket_name)
            return bucket.exists()
        except Exception:
            logger.exception(f"Failed to check if bucket {bucket_name} exists")
            return False
