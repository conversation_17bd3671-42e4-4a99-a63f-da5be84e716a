from abc import ABC, abstractmethod
from typing import Any

from app.integrations.base.backend import BaseBackend
from app.integrations.schemas import DocumentData, FileData
from app.integrations.types import BackendType


class BaseFileBackend(BaseBackend, ABC):
    @property
    def backend_type(self) -> BackendType:
        return BackendType.FILE

    @abstractmethod
    async def start_processing(
        self,
        bucket_names: list[str],
    ) -> dict[str, Any]:
        pass

    @abstractmethod
    async def search_files(
        self,
        query: str,
        limit: int = 10,
    ) -> list[tuple[DocumentData, float]]:
        pass

    @abstractmethod
    async def list_files(
        self,
        container_name: str,
    ) -> list[FileData]:
        pass

    @abstractmethod
    async def create_bucket(self, bucket_name: str) -> None:
        pass

    @abstractmethod
    async def bucket_exists(self, bucket_name: str) -> bool:
        pass
