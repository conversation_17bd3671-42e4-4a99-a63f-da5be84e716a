from sqlalchemy import String, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column

from app.common.orm.types import StringEnum
from app.integrations.base.tenant_model import TenantModel
from app.integrations.types import IntegrationSource


class CRMAccountAccess(TenantModel):
    __tablename__ = "crm_account_access"

    source: Mapped[IntegrationSource] = mapped_column(
        StringEnum(IntegrationSource, length=20), nullable=False
    )
    crm_user_id: Mapped[str] = mapped_column(String(50), nullable=False, index=True)
    crm_account_id: Mapped[str] = mapped_column(String(50), nullable=False, index=True)
    crm_account_name: Mapped[str] = mapped_column(String(255), nullable=False)
    crm_access_type: Mapped[str] = mapped_column(String(100), nullable=False)
    crm_access_role: Mapped[str] = mapped_column(String(100), nullable=True)
    summary: Mapped[str | None] = mapped_column(Text, nullable=True)

    __table_args__ = (
        UniqueConstraint(
            "tenant_id",
            "source",
            "crm_user_id",
            "crm_account_id",
            name="uq_crm_access_user_account",
        ),
    )
