from datetime import datetime
from typing import Binary<PERSON>

from app.common.helpers.logger import get_logger
from app.workspace.exceptions import FileAlreadyExistsError
from app.workspace.integrations.user_integrations import UserIntegrations
from app.workspace.schemas.file import TeamDocument, UploadTeamDocumentResponse

logger = get_logger()


class FileService:
    def __init__(self, user_integrations: UserIntegrations):
        self.user_integrations = user_integrations

    async def get_team_documents(self) -> list[TeamDocument]:
        file_provider = await self.user_integrations.file()
        if not file_provider:
            return []

        container_name = f"pearl-org-{self.user_integrations.org_id}"

        await self._ensure_bucket_exists(file_provider, container_name)

        files = await file_provider.list_files(container_name=container_name)

        files.sort(key=lambda f: f.last_modified, reverse=True)

        return [
            TeamDocument(
                id=file.name,  # Use file path/name for direct access
                title=file.name,
                format=file.name.split(".")[-1].lower()
                if "." in file.name
                else "unknown",
                date_uploaded=file.time_created,
            )
            for file in files
        ]

    async def _ensure_bucket_exists(self, file_provider, bucket_name: str) -> None:
        bucket_exists = await file_provider.bucket_exists(bucket_name)
        if not bucket_exists:
            try:
                await file_provider.create_bucket(bucket_name)
            except Exception as e:
                logger.warning(f"Failed to create bucket {bucket_name}: {str(e)}")

    async def delete_team_document(self, file_name: str) -> None:
        """Delete a team document by file name."""
        file_provider = await self.user_integrations.file()
        if not file_provider:
            raise ValueError("File provider not available")

        container_name = f"pearl-org-{self.user_integrations.org_id}"

        try:
            # Delete the file directly using its name
            await file_provider.delete_file(
                container_name=container_name, file_name=file_name
            )
        except Exception as e:
            raise ValueError(f"Failed to delete document '{file_name}': {str(e)}")

    async def upload_team_document(
        self, file_obj: BinaryIO, file_name: str | None
    ) -> UploadTeamDocumentResponse:
        if not file_name:
            raise ValueError("File name is required")

        file_provider = await self.user_integrations.file()
        if not file_provider:
            raise ValueError("File provider not available")

        container_name = f"pearl-org-{self.user_integrations.org_id}"

        await self._ensure_bucket_exists(file_provider, container_name)

        await self._validate_file_name_uniqueness(
            file_provider, container_name, file_name
        )

        await file_provider.upload_file(container_name, file_obj, file_name)

        return UploadTeamDocumentResponse(
            id=file_name,  # Use file name as ID for direct access
            title=file_name,
            format=file_name.split(".")[-1].lower(),
            date_uploaded=datetime.now().isoformat(),
        )

    async def _validate_file_name_uniqueness(
        self, file_provider, container_name: str, file_name: str
    ) -> None:
        """Check if a file with the same name already exists."""
        files = await file_provider.list_files(container_name)
        existing_file_names = {file.name for file in files}

        if file_name in existing_file_names:
            raise FileAlreadyExistsError(
                f"A file with the name '{file_name}' already exists"
            )

    async def trigger_file_processing(self, container_name: str) -> None:
        file_provider = await self.user_integrations.file()
        if not file_provider:
            return

        try:
            await file_provider.start_processing([container_name])
        except Exception as e:
            logger.exception(
                f"Failed to trigger file processing for container {container_name}: {e}"
            )
