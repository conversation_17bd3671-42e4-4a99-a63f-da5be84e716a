from fastapi import APIRouter, Query

from app.auth.dependencies import AuthenticatedUserIdDep
from app.common.helpers.logger import get_logger
from app.workspace.dependencies import (
    HubSpotConnectionServiceDep,
    UserEnvDep,
)

logger = get_logger()

router = APIRouter()


@router.get("/hubspot/auth-url")
async def get_hubspot_auth_url(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    service: HubSpotConnectionServiceDep,
):
    oauth_uri = await service.generate_oauth_authorization_uri(
        user_id=user_id,
        environment=user_env,
    )

    return {"auth_url": oauth_uri}


@router.get("/hubspot/callback")
async def process_hubspot_callback(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    service: HubSpotConnectionServiceDep,
    code: str = Query(...),
    state: str = Query(...),
):
    crm_token = await service.process_oauth_callback(
        user_id=user_id,
        environment=user_env,
        code=code,
        state=state,
    )

    return {
        "message": "OAuth succeed",
        "crm_user_id": crm_token.external_user_id,
        "user_id": str(user_id),
    }


@router.delete("/hubspot/connection")
async def remove_hubspot_connection(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    service: HubSpotConnectionServiceDep,
):
    await service.remove_connection(
        user_id=user_id,
        environment=user_env,
    )

    return {"message": "HubSpot connection removed successfully"}
